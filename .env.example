# Environment Configuration Template
# Copy this file to .env and update the values as needed

# Server Configuration
NODE_ENV=development
PORT=3000

# MongoDB Configuration
# For local development (without Docker)
MONGODB_URI=mongodb://localhost:27017/inspirator-api
# For Docker Compose (uncomment the line below and comment the one above)
# MONGODB_URI=*************************************************************************
MONGODB_DB_NAME=inspirator-api

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
ADMIN_TOKEN=your-admin-token-for-delete-operations

# CORS
CORS_ORIGIN=*

# MongoDB Docker Configuration (used by docker-compose.yml)
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password123

# Mongo Express Configuration (Optional web UI for MongoDB)
ME_CONFIG_BASICAUTH_USERNAME=admin
ME_CONFIG_BASICAUTH_PASSWORD=admin123
