{"openapi": "3.0.3", "info": {"title": "Inspirator API", "description": "API for managing inspirational sentences with image URLs", "version": "1.0.0", "contact": {"name": "API Support"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}], "paths": {"/health": {"get": {"summary": "Health check endpoint", "description": "Check if the server and database are healthy", "tags": ["Health"], "responses": {"200": {"description": "Server is healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}, "/api/sentences/upload": {"post": {"summary": "Upload a new sentence", "description": "Create a new inspirational sentence with content, name, and image URL", "tags": ["Sentences"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSentenceRequest"}}}}, "responses": {"201": {"description": "Sentence uploaded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SentenceApiResponse"}}}}, "400": {"description": "Validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/sentences/{id}": {"get": {"summary": "Get sentence by ID", "description": "Retrieve a specific sentence by its UUID", "tags": ["Sentences"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "UUID of the sentence", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Sentence found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SentenceApiResponse"}}}}, "400": {"description": "Invalid ID format", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Sentence not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"summary": "Delete sentence by ID", "description": "Delete a specific sentence by its UUID (requires admin authentication)", "tags": ["Sentences"], "security": [{"AdminToken": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "UUID of the sentence to delete", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Sentence deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "Invalid ID format", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing admin token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Sentence not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/sentences/random": {"get": {"summary": "Get random sentence", "description": "Retrieve a random inspirational sentence", "tags": ["Sentences"], "responses": {"200": {"description": "Random sentence retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SentenceApiResponse"}}}}, "404": {"description": "No sentences found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/sentences/all": {"get": {"summary": "Get all sentences", "description": "Retrieve all inspirational sentences", "tags": ["Sentences"], "responses": {"200": {"description": "All sentences retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SentenceListApiResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/sentences/range": {"get": {"summary": "Get sentences with pagination", "description": "Retrieve sentences with pagination support", "tags": ["Sentences"], "parameters": [{"name": "skip", "in": "query", "description": "Number of sentences to skip", "schema": {"type": "integer", "minimum": 0, "default": 0}}, {"name": "limit", "in": "query", "description": "Maximum number of sentences to return", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "Sentences retrieved with pagination", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSentenceResponse"}}}}, "400": {"description": "Invalid pagination parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"securitySchemes": {"AdminToken": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Admin JWT token for authentication"}}, "schemas": {"CreateSentenceRequest": {"type": "object", "required": ["content", "name", "img_url"], "properties": {"content": {"type": "string", "description": "The inspirational sentence content", "minLength": 1, "maxLength": 1000, "example": "The only way to do great work is to love what you do."}, "name": {"type": "string", "description": "Name of the person who submitted the sentence", "minLength": 1, "maxLength": 100, "example": "<PERSON>"}, "img_url": {"type": "string", "format": "uri", "description": "URL of an image associated with the sentence", "example": "https://example.com/image.jpg"}}}, "SentenceResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the sentence", "example": "123e4567-e89b-12d3-a456-************"}, "content": {"type": "string", "description": "The inspirational sentence content", "example": "The only way to do great work is to love what you do."}, "name": {"type": "string", "description": "Name of the person who submitted the sentence", "example": "<PERSON>"}, "img_url": {"type": "string", "format": "uri", "description": "URL of an image associated with the sentence", "example": "https://example.com/image.jpg"}, "timestamp": {"type": "string", "format": "date-time", "description": "When the sentence was created", "example": "2023-12-01T10:30:00.000Z"}, "ip": {"type": "string", "description": "IP address of the client who submitted the sentence", "example": "***********"}}}, "ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates if the request was successful"}, "message": {"type": "string", "description": "Optional message describing the result"}, "error": {"type": "string", "description": "Error message if the request failed"}}}, "SentenceApiResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/SentenceResponse"}}}]}, "SentenceListApiResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/SentenceResponse"}}}}]}, "PaginationInfo": {"type": "object", "properties": {"skip": {"type": "integer", "description": "Number of sentences skipped", "example": 0}, "limit": {"type": "integer", "description": "Maximum number of sentences returned", "example": 10}, "total": {"type": "integer", "description": "Total number of sentences available", "example": 100}, "hasMore": {"type": "boolean", "description": "Whether there are more sentences available", "example": true}}}, "PaginatedSentenceResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"sentences": {"type": "array", "items": {"$ref": "#/components/schemas/SentenceResponse"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}}}]}, "HealthResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time", "description": "Current server timestamp", "example": "2023-12-01T10:30:00.000Z"}, "database": {"type": "string", "enum": ["connected", "disconnected"], "description": "Database connection status", "example": "connected"}}}}}]}, "ErrorResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"success": {"type": "boolean", "enum": [false]}, "data": {"type": "object", "properties": {"errors": {"type": "array", "items": {"type": "string"}, "description": "List of validation errors"}}}}}]}, "SuccessResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"success": {"type": "boolean", "enum": [true]}}}]}}}}