// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the inspirator-api database
db = db.getSiblingDB('inspirator-api');

// Create a user for the application
db.createUser({
  user: 'apiuser',
  pwd: 'apipassword',
  roles: [
    {
      role: 'readWrite',
      db: 'inspirator-api'
    }
  ]
});

// Create collections and indexes if needed
db.createCollection('sentences');

// Create indexes for better performance
db.sentences.createIndex({ "id": 1 }, { unique: true });
db.sentences.createIndex({ "createdAt": 1 });
db.sentences.createIndex({ "category": 1 });

print('Database initialized successfully');
