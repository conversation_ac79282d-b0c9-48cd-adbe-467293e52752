{"name": "inspirator-api", "module": "index.ts", "type": "module", "private": true, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/bun": "latest", "@types/cors": "^2.8.19", "@types/jsonwebtoken": "^9.0.10", "@types/uuid": "^10.0.0"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@types/swagger-ui-express": "^4.1.8", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}}