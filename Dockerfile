# Use the official Bun image
FROM oven/bun:1.1.34-alpine AS base

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json bun.lock* ./

# Install dependencies
RUN bun install --frozen-lockfile --production

# Copy source code
COPY . .

# Build the application (if needed)
RUN bun build index.ts --outdir ./dist --target bun

# Expose the port
EXPOSE 3000

# Add curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S bunjs -u 1001

# Change ownership of the app directory
RUN chown -R bunjs:nodejs /app
USER bunjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start the application
CMD ["bun", "run", "index.ts"]
