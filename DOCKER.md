# Docker Setup for Inspirator API

This document explains how to run the Inspirator API with MongoDB using Docker Compose.

## Prerequisites

- Docker
- Docker Compose

## Quick Start

1. **Clone the repository and navigate to the project directory**
   ```bash
   cd inspirator-api
   ```

2. **Start the services**
   ```bash
   docker-compose up -d
   ```

3. **Check the status**
   ```bash
   docker-compose ps
   ```

4. **View logs**
   ```bash
   # All services
   docker-compose logs -f
   
   # Specific service
   docker-compose logs -f api
   docker-compose logs -f mongodb
   ```

## Services

The Docker Compose setup includes:

### 1. MongoDB (`mongodb`)
- **Image**: `mongo:7.0`
- **Port**: `27017`
- **Database**: `inspirator-api`
- **Admin User**: `admin` / `password123`
- **Data**: Persisted in Docker volume `mongodb_data`

### 2. API (`api`)
- **Built from**: Local Dockerfile
- **Port**: `3000`
- **Environment**: Production-ready configuration
- **Health Check**: Available at `/health`

### 3. Mongo Express (`mongo-express`) - Optional
- **Image**: `mongo-express:1.0.2`
- **Port**: `8081`
- **Web UI**: MongoDB administration interface
- **Login**: `admin` / `admin123`

## Environment Variables

The services use the following environment variables:

### API Service
- `NODE_ENV=production`
- `PORT=3000`
- `MONGODB_URI=*************************************************************************`
- `MONGODB_DB_NAME=inspirator-api`
- `CORS_ORIGIN=*`

### MongoDB Service
- `MONGO_INITDB_ROOT_USERNAME=admin`
- `MONGO_INITDB_ROOT_PASSWORD=password123`
- `MONGO_INITDB_DATABASE=inspirator-api`

## Accessing the Services

Once running, you can access:

- **API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api-docs
- **Health Check**: http://localhost:3000/health
- **MongoDB**: `***************************************************************************`
- **Mongo Express** (optional): http://localhost:8081

## Common Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### Rebuild and start
```bash
docker-compose up -d --build
```

### View logs
```bash
docker-compose logs -f
```

### Execute commands in containers
```bash
# Access MongoDB shell
docker-compose exec mongodb mongosh -u admin -p password123 --authenticationDatabase admin

# Access API container
docker-compose exec api sh
```

### Clean up (removes containers, networks, and volumes)
```bash
docker-compose down -v
```

## Development vs Production

### Development
For development, you might want to:
1. Mount your source code as a volume for hot reloading
2. Use a local MongoDB instance
3. Set `NODE_ENV=development`

### Production
The current setup is production-ready with:
- Health checks for all services
- Proper networking between containers
- Data persistence
- Security considerations (non-root user in API container)

## Troubleshooting

### API can't connect to MongoDB
1. Check if MongoDB is healthy: `docker-compose ps`
2. Check MongoDB logs: `docker-compose logs mongodb`
3. Verify network connectivity: `docker-compose exec api ping mongodb`

### Port conflicts
If ports 3000, 8081, or 27017 are already in use:
1. Stop the conflicting services
2. Or modify the ports in `docker-compose.yml`

### Data persistence
MongoDB data is stored in a Docker volume. To reset the database:
```bash
docker-compose down -v
docker-compose up -d
```

## Security Notes

**Important**: The default passwords in this setup are for development only. 
For production deployment:

1. Change all default passwords
2. Use environment files or secrets management
3. Configure proper network security
4. Enable MongoDB authentication and SSL
5. Use specific CORS origins instead of `*`
