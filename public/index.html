<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspirator API</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin: 2rem 0;
        }
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        .endpoints {
            text-align: left;
            margin-top: 3rem;
            background: rgba(0, 0, 0, 0.1);
            padding: 2rem;
            border-radius: 15px;
        }
        .endpoint {
            margin: 1rem 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
        }
        .method {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 1rem;
            min-width: 60px;
            text-align: center;
        }
        .get { background: #28a745; }
        .post { background: #007bff; }
        .delete { background: #dc3545; }
        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Inspirator API</h1>
        <p class="subtitle">Manage and share inspirational sentences with beautiful images</p>
        
        <div class="buttons">
            <a href="/api-docs" class="btn btn-primary">
                📖 View API Documentation
            </a>
            <a href="/swagger.json" class="btn btn-secondary">
                📄 Download OpenAPI Spec
            </a>
            <a href="/health" class="btn btn-secondary">
                ❤️ Health Check
            </a>
        </div>

        <div class="endpoints">
            <h3>🔗 Available Endpoints</h3>
            <div class="endpoint">
                <span class="method post">POST</span>
                <code>/api/sentences/upload</code> - Upload a new sentence
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <code>/api/sentences/:id</code> - Get sentence by ID
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <code>/api/sentences/random</code> - Get random sentence
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <code>/api/sentences/all</code> - Get all sentences
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <code>/api/sentences/range</code> - Get sentences with pagination
            </div>
            <div class="endpoint">
                <span class="method delete">DELETE</span>
                <code>/api/sentences/:id</code> - Delete sentence (requires admin token)
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <code>/health</code> - Health check
            </div>
        </div>

        <div class="status" id="status">
            <strong>Server Status:</strong> <span id="server-status">Checking...</span><br>
            <strong>Database:</strong> <span id="db-status">Checking...</span>
        </div>
    </div>

    <script>
        // Check server health on page load
        fetch('/health')
            .then(response => response.json())
            .then(data => {
                document.getElementById('server-status').textContent = data.success ? '✅ Online' : '❌ Offline';
                document.getElementById('db-status').textContent = 
                    data.data?.database === 'connected' ? '✅ Connected' : '❌ Disconnected';
            })
            .catch(error => {
                document.getElementById('server-status').textContent = '❌ Offline';
                document.getElementById('db-status').textContent = '❌ Unknown';
            });
    </script>
</body>
</html>
